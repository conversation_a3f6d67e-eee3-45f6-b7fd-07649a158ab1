/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:15:29
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-29 19:15:32
 * @Description: 
 * 
 */
export interface EmulatorCore {
  id: string
  name: string
  extensions: string[]
  description: string
  cdnUrl: string
}

export interface GameFile {
  name: string
  extension: string
  data: ArrayBuffer
  size: number
  system?: string
}

export interface EmulatorConfig {
  core: string
  game: string
  width?: number
  height?: number
  sound?: boolean
  fastForward?: boolean
}

export interface EmulatorState {
  isLoading: boolean
  isPlaying: boolean
  error: string | null
  currentGame: GameFile | null
  selectedCore: EmulatorCore | null
} 