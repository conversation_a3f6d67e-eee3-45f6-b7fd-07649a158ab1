@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white min-h-screen;
    background-attachment: fixed;
  }

  * {
    @apply box-border;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 active:scale-95;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 active:scale-95;
  }

  .card {
    @apply bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm border border-gray-600/50 rounded-2xl shadow-2xl;
  }

  .game-card {
    @apply card p-6 hover:border-blue-500/50 transform hover:scale-105 transition-all duration-300 cursor-pointer group;
  }

  .game-card:hover {
    @apply shadow-blue-500/20;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500;
  }

  .emulator-container {
    @apply relative w-full h-full bg-black rounded-xl overflow-hidden shadow-2xl;
  }

  .mobile-fullscreen {
    @apply fixed inset-0 z-50 bg-black;
  }

  .game-controls {
    @apply flex flex-wrap gap-3 justify-center items-center p-4 bg-gradient-to-r from-gray-800/90 to-gray-700/90 backdrop-blur-sm rounded-xl;
  }

  .status-indicator {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .status-loading {
    @apply status-indicator bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
  }

  .status-playing {
    @apply status-indicator bg-green-500/20 text-green-400 border border-green-500/30;
  }

  .status-error {
    @apply status-indicator bg-red-500/20 text-red-400 border border-red-500/30;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-blue-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-blue-500;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mobile-optimized {
    @apply text-sm;
  }

  .mobile-padding {
    @apply px-4 py-2;
  }
}

/* 游戏画布样式 */
#canvas {
  @apply w-full h-full object-contain;
}

/* 响应式游戏容器 */
.game-viewport {
  aspect-ratio: 4/3;
  max-height: 70vh;
}

@media (max-width: 768px) {
  .game-viewport {
    aspect-ratio: 16/9;
    max-height: none;
    height: 100vh;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 加载动画 */
.pulse-ring {
  @apply absolute inset-0 rounded-full border-4 border-blue-500/30 animate-ping;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}