<!DOCTYPE html>
<html>
<head>
    <title>测试修复</title>
</head>
<body>
    <script>
        // 模拟 EJS_Runtime 为不可删除的属性
        Object.defineProperty(window, 'EJS_Runtime', {
            value: 'test',
            configurable: false,
            writable: false
        });

        // 添加一些可删除的属性
        window.EJS_test1 = 'value1';
        window.EJS_test2 = 'value2';

        console.log('修复前的属性:');
        console.log('EJS_Runtime:', window.EJS_Runtime);
        console.log('EJS_test1:', window.EJS_test1);
        console.log('EJS_test2:', window.EJS_test2);

        // 使用修复后的清理逻辑
        const ejsKeys = Object.keys(window).filter((k) => k.startsWith("EJS_"));
        ejsKeys.forEach((key) => {
            try {
                const descriptor = Object.getOwnPropertyDescriptor(window, key);
                if (descriptor && descriptor.configurable !== false) {
                    delete window[key];
                    console.log(`成功删除属性: ${key}`);
                } else {
                    console.log(`无法删除不可配置的属性: ${key}`);
                }
            } catch (e) {
                console.log(`删除属性 ${key} 时出错:`, e);
            }
        });

        console.log('修复后的属性:');
        console.log('EJS_Runtime:', window.EJS_Runtime);
        console.log('EJS_test1:', window.EJS_test1);
        console.log('EJS_test2:', window.EJS_test2);
    </script>
</body>
</html>
