{"name": "game-emulator-web", "version": "1.0.0", "description": "Web-based game emulator with automatic core selection", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "generate-games": "node scripts/generate-games-config.js"}, "dependencies": {"@emulatorjs/core-gambatte": "^4.2.3", "@emulatorjs/cores": "^4.2.3", "@emulatorjs/emulatorjs": "^4.2.3", "jszip": "^3.10.1", "vue": "^3.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "typescript": "^5.2.0", "vite": "^4.4.0", "vue-tsc": "^1.8.0"}}